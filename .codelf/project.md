## Claude Code MCP Tool (claude-code-mcp)

> 一个 MCP 工具，用于下载和注册 GitHub 仓库中的 claude-code sub-agents 和 slash-commands

> 通过接受 github-repo 参数，自动下载存储在项目根目录下的 `.xclaude` 目录下，并注册相关工具

> 开发中 - 初始化阶段

> 个人项目

> TypeScript + Node.js + MCP SDK

## Dependencies

- @modelcontextprotocol/sdk: MCP 框架核心依赖
- node-fetch: HTTP 请求库，用于下载 GitHub 文件
- fs-extra: 文件系统操作增强库
- path: Node.js 路径处理模块

## Development Environment

> Node.js 环境 (推荐 v18+)
> TypeScript 编译环境
> MCP 开发工具链

## Structure

> 完整的 MCP 工具实现，包含核心功能、工具函数、类型定义和文档

```
root/
├── src/                    # 源代码目录
│   ├── index.ts           # 主入口文件，MCP 服务器初始化和 stdio 传输
│   ├── tools/             # 工具实现目录
│   │   └── claude-code.ts # claude-code MCP 工具核心实现，支持工具注册
│   ├── utils/             # 工具函数目录
│   │   ├── github.ts      # GitHub API 相关工具函数：下载、验证、内容获取
│   │   ├── file.ts        # 文件操作相关工具函数：读写、目录管理
│   │   └── validation.ts  # 输入验证工具函数：URL 解析、路径安全检查
│   └── types/             # 类型定义目录
│       ├── github.ts      # GitHub API 相关类型定义
│       └── mcp.ts         # MCP 工具相关类型定义
├── dist/                  # 构建输出目录（TypeScript 编译后的 JavaScript）
├── package.json           # 项目依赖配置，包含构建和测试脚本
├── tsconfig.json          # TypeScript 配置，ES2020 + ESM
├── test-tool.js           # 简单测试脚本，验证工具注册
├── README.md              # 项目说明文档
├── USAGE.md               # 详细使用指南和示例
├── .gitignore             # Git 忽略文件配置
└── .codelf/               # 项目元信息目录
    ├── project.md         # 项目描述文件
    ├── attention.md       # 开发注意事项
    └── _changelog.md      # 变更日志
```
