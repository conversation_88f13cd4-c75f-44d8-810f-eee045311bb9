# Claude Code MCP Tool

A Model Context Protocol (MCP) tool for downloading and registering claude-code agents and commands from GitHub repositories as individual MCP tools.

## Features

- 🚀 **Git Clone Download**: Uses `git clone --depth=1` for efficient repository downloading
- 🔧 **Individual Tool Registration**: Each agent and command becomes a separate MCP tool
- 📁 **Local Detection**: Automatically detects and uses local `.xclaude` directory
- 🎯 **Direct Tool Access**: Call agents and commands directly without wrapper tools
- 🧹 **Smart Cleanup**: Automatically removes unnecessary files and directories
- ⚡ **Fast & Lightweight**: Only downloads what's needed
- 🔄 **Format Compatibility**: Automatically converts `.claude` references to `.xclaude`

## Installation

```bash
# Clone the project
git clone <repository-url>
cd claude-code-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

## Usage

### Basic Usage

#### Configuration

Set the GitHub repository via environment variable:

```bash
export CLAUDE_CODE_REPO="cexll/myclaude"
```

Or configure in code:

```typescript
import { registerClaudeCodeTool } from 'claude-code-mcp';

registerClaudeCodeTool(server, 'cexll/myclaude');
```

#### Direct Tool Usage

Individual agents and commands become available as separate MCP tools:

```typescript
// Use an agent
use code-reviewer-agent with task="Review my latest changes"

// Use a command
use git-commit-command with arguments="Fix authentication bug"
```

### Auto-Detection

The tool automatically detects local `.xclaude` directory on startup and registers all agents and commands as individual MCP tools.

## Tool Parameters

### Configuration

| Parameter    | Type   | Required | Description                                |
| ------------ | ------ | -------- | ------------------------------------------ |
| `githubRepo` | string | ✅       | GitHub repository URL or owner/repo format |

### Individual Tools

Each agent and command becomes a separate MCP tool:

- **Agents**: `{name}-agent` (e.g., `code-reviewer-agent`)
  - Parameter: `task` (string) - Task description for the agent
- **Commands**: `{filename}-command` (e.g., `git-commit-command`)
  - Parameter: `arguments` (optional string) - Replaces $ARGUMENTS in content

**Notes**:

- Files are stored in the `.xclaude` directory for compatibility with other AI tools
- Tool automatically detects and uses local `.xclaude` directory if it exists
- Uses `git clone --depth=1` for efficient downloading
- Automatically removes unnecessary files and directories

## Supported Repository Formats

- **Full URL**: `https://github.com/cexll/myclaude`
- **Short format**: `cexll/myclaude`

## Directory Structure

### Expected Repository Structure

```
repository/
├── agents/             # Agent definitions
│   ├── agent1.md
│   └── agent2.md
└── commands/           # Command definitions
    ├── command1.md
    └── command2.md
```

### Local Storage Structure

```
.xclaude/
├── agents/
│   ├── agent1.md
│   └── agent2.md
└── commands/
    ├── command1.md
    └── command2.md
```

## 开发

```bash
# 开发模式（监听文件变化）
npm run dev

# 构建
npm run build

# 启动
npm start

# 清理构建文件
npm run clean
```

## 配置文件支持

工具支持两种配置方式：

### 1. 配置文件方式

在仓库根目录创建 `config.json` 或 `claude-code.json`：

```json
{
  "subAgents": [
    {
      "name": "requirements-pilot",
      "description": "需求分析和项目规划助手",
      "prompt": "...",
      "filePath": "sub-agents/requirements-pilot.md"
    }
  ],
  "slashCommands": [
    {
      "name": "analyze",
      "description": "代码分析命令",
      "prompt": "...",
      "filePath": "slash-commands/analyze.md"
    }
  ]
}
```

### 2. 自动发现方式

如果没有配置文件，工具会自动发现仓库中的 Markdown 文件并注册为工具。

## 安全特性

- ✅ 路径遍历攻击防护
- ✅ 输入参数验证
- ✅ GitHub API 错误处理
- ✅ 文件权限检查

## 错误处理

工具包含完善的错误处理机制：

- GitHub 仓库不存在或无法访问
- 网络连接问题
- 文件写入权限问题
- 无效的输入参数

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
