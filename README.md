# Claude Code MCP Server

A Model Context Protocol (MCP) server for downloading and registering claude-code agents and commands from GitHub repositories as individual MCP tools.

## Features

- 🚀 **Git Clone Download**: Uses `git clone --depth=1` for efficient repository downloading
- 🔧 **Individual Tool Registration**: Each agent and command becomes a separate MCP tool
- 📁 **Local Detection**: Automatically detects and uses local `.xclaude` directory
- 🎯 **Direct Tool Access**: Call agents and commands directly without wrapper tools
- 🧹 **Smart Cleanup**: Automatically removes unnecessary files and directories
- ⚡ **Fast & Lightweight**: Only downloads what's needed
- 🔄 **Format Compatibility**: Automatically converts `.claude` references to `.xclaude`
- 🔄 **Auto-Registration**: Automatically registers tools on server startup

## Installation

```bash
# Clone the project
git clone <repository-url>
cd claude-code-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

## Usage

### Start the MCP Server

```bash
# Set GitHub repository (optional)
export CLAUDE_CODE_REPO="cexll/myclaude"

# Start the server
node dist/index.js
```

### MCP Client Configuration

Add to your MCP client configuration (e.g., <PERSON>ktop):

```json
{
  "mcpServers": {
    "xclaude": {
      "command": "node",
      "args": ["/absolute/path/to/claude-code-mcp/dist/index.js"],
      "env": {
        "CLAUDE_CODE_REPO": "cexll/myclaude"
      }
    }
  }
}
```

### Using the Tools

Once connected, individual agents and commands become available as separate MCP tools:

```bash
# Use an agent
use bmad-architect-agent with task="Design a user authentication system"

# Use a command
use bugfix-command with arguments="Fix login validation issue"

# Check status
use claude-code-status
```

### Auto-Detection

The server automatically:

- Detects local `.xclaude` directory on startup
- Downloads from GitHub if no local directory exists
- Registers all agents and commands as individual MCP tools

## Tool Parameters

### Configuration

| Parameter    | Type   | Required | Description                                |
| ------------ | ------ | -------- | ------------------------------------------ |
| `githubRepo` | string | ✅       | GitHub repository URL or owner/repo format |

### Individual Tools

Each agent and command becomes a separate MCP tool:

- **Agents**: `{name}-agent` (e.g., `code-reviewer-agent`)
  - Parameter: `task` (string) - Task description for the agent
- **Commands**: `{filename}-command` (e.g., `git-commit-command`)
  - Parameter: `arguments` (optional string) - Replaces $ARGUMENTS in content

**Notes**:

- Files are stored in the `.xclaude` directory for compatibility with other AI tools
- Tool automatically detects and uses local `.xclaude` directory if it exists
- Uses `git clone --depth=1` for efficient downloading
- Automatically removes unnecessary files and directories

## Supported Repository Formats

- **Full URL**: `https://github.com/cexll/myclaude`
- **Short format**: `cexll/myclaude`

## Directory Structure

### Expected Repository Structure

```
repository/
├── agents/             # Agent definitions
│   ├── agent1.md
│   └── agent2.md
└── commands/           # Command definitions
    ├── command1.md
    └── command2.md
```

### Local Storage Structure

```
.xclaude/
├── agents/
│   ├── agent1.md
│   └── agent2.md
└── commands/
    ├── command1.md
    └── command2.md
```

## Development

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Run tests
npm run test:unit        # Unit tests only
npm run test:integration # Integration tests only
npm run test:all         # All tests

# Development mode (watch for changes)
npm run dev

# Clean build files
npm run clean
```

## Testing

The project includes comprehensive test coverage:

- **Unit Tests**: Test core utility functions and markdown processing
- **Integration Tests**: Test the complete MCP server functionality

```bash
# Run specific test suites
npm run test:unit        # Fast unit tests
npm run test:integration # Full MCP server tests
npm run test:all         # Complete test suite
```

## 配置文件支持

工具支持两种配置方式：

### 1. 配置文件方式

在仓库根目录创建 `config.json` 或 `claude-code.json`：

```json
{
  "subAgents": [
    {
      "name": "requirements-pilot",
      "description": "需求分析和项目规划助手",
      "prompt": "...",
      "filePath": "sub-agents/requirements-pilot.md"
    }
  ],
  "slashCommands": [
    {
      "name": "analyze",
      "description": "代码分析命令",
      "prompt": "...",
      "filePath": "slash-commands/analyze.md"
    }
  ]
}
```

### 2. 自动发现方式

如果没有配置文件，工具会自动发现仓库中的 Markdown 文件并注册为工具。

## 安全特性

- ✅ 路径遍历攻击防护
- ✅ 输入参数验证
- ✅ GitHub API 错误处理
- ✅ 文件权限检查

## 错误处理

工具包含完善的错误处理机制：

- GitHub 仓库不存在或无法访问
- 网络连接问题
- 文件写入权限问题
- 无效的输入参数

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
