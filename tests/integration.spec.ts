/**
 * Claude Code MCP 集成测试
 */

import { describe, beforeAll, afterAll, test, expect } from 'vitest';
import { MCPTestClient } from 'mcp-test-client';
import fs from 'fs-extra';

describe('Claude Code MCP Integration Tests', () => {
  let client: MCPTestClient;

  beforeAll(async () => {
    // 确保没有现有的 .xclaude 目录干扰测试
    if (await fs.pathExists('.xclaude')) {
      await fs.move('.xclaude', '.xclaude-backup');
    }

    client = new MCPTestClient({
      serverCommand: 'node',
      serverArgs: ['./dist/index.js'],
      serverEnv: {
        XCLAUDE_GITHUB_REPO: 'cexll/myclaude'
      }
    });

    await client.init();
  });

  afterAll(async () => {
    if (client) {
      await client.cleanup();
    }

    // 恢复备份的 .xclaude 目录
    if (await fs.pathExists('.xclaude-backup')) {
      await fs.move('.xclaude-backup', '.xclaude');
    }

    // 清理测试下载的目录
    if (await fs.pathExists('.xclaude')) {
      await fs.remove('.xclaude');
    }
  });

  test('should download and register tools from GitHub', async () => {
    // 等待服务器初始化和下载完成
    await new Promise(resolve => setTimeout(resolve, 15000));

    // 检查是否下载了 .xclaude 目录
    expect(await fs.pathExists('.xclaude')).toBe(true);

    // 检查是否有 agents 和 commands 目录
    const hasAgents = await fs.pathExists('.xclaude/agents');
    const hasCommands = await fs.pathExists('.xclaude/commands');

    console.log(`Downloaded agents: ${hasAgents}, commands: ${hasCommands}`);

    // 至少应该有其中一个目录
    expect(hasAgents || hasCommands).toBe(true);
  }, 20000); // 20秒超时

  test('should register tools from downloaded content', async () => {
    try {
      const tools = await client.listTools();
      expect(tools).toBeDefined();
      expect(Array.isArray(tools)).toBe(true);

      const toolNames = tools.map(tool => tool.name);
      console.log('Registered tools:', toolNames);

      // 检查是否有注册的工具
      const hasAgents = toolNames.some(name => name.includes('-agent'));
      const hasCommands = toolNames.some(name => name.includes('-command'));

      console.log(`Registered agents: ${hasAgents}, commands: ${hasCommands}`);

      // 应该至少注册了一些工具
      expect(toolNames.length).toBeGreaterThan(0);
    } catch (error: any) {
      console.log('List tools error:', error.message);
      // 如果 listTools 不可用，至少验证服务器在运行
      expect(client).toBeDefined();
    }
  });

  test('should handle tool calls if tools are available', async () => {
    try {
      const tools = await client.listTools();

      if (tools && tools.length > 0) {
        const firstTool = tools[0];
        console.log(`Testing tool: ${firstTool.name}`);

        // 尝试调用第一个工具
        try {
          let result: any;
          if (firstTool.name.includes('-agent')) {
            result = await client.callTool(firstTool.name, {
              task: 'Test task for integration testing'
            });
          } else if (firstTool.name.includes('-command')) {
            result = await client.callTool(firstTool.name, {
              arguments: 'test arguments'
            });
          } else {
            result = await client.callTool(firstTool.name, {});
          }

          expect(result).toBeDefined();
          if (result.content) {
            console.log('Tool call successful:', result.content[0]?.text?.substring(0, 100) + '...');
          } else {
            console.log('Tool call successful but no content returned');
          }
        } catch (error: any) {
          console.log('Tool call failed (may be expected):', error.message);
          // 工具调用失败可能是正常的，取决于工具的具体要求
        }
      } else {
        console.log('No tools available for testing');
      }
    } catch (error: any) {
      console.log('Tool testing error:', error.message);
    }
  });
});
