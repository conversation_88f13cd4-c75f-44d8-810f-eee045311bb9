/**
 * Claude Code MCP 工具测试
 */

import { describe, beforeAll, afterAll, test, expect } from 'vitest';
import { MCPTestClient } from 'mcp-test-client';
import fs from 'fs-extra';
import path from 'path';
import { parseGitHubRepo } from '../src/utils/validation.js';
import { processMarkdownFile } from '../src/utils/markdown.js';

describe('Claude Code MCP Tools Tests', () => {
  const testXclaudeDir = '.xclaude-test';

  beforeAll(async () => {
    // 创建测试用的 .xclaude 目录
    await setupTestXclaudeDirectory();
  });

  afterAll(async () => {
    // 清理测试目录
    await fs.remove(testXclaudeDir);
  });

  describe('Claude Code MCP Integration Tests', () => {
    let client: MCPTestClient;

    beforeAll(async () => {
      // 确保没有现有的 .xclaude 目录干扰测试
      if (await fs.pathExists('.xclaude')) {
        await fs.move('.xclaude', '.xclaude-backup');
      }

      client = new MCPTestClient({
        serverCommand: 'node',
        serverArgs: ['./dist/index.js'],
        serverEnv: {
          CLAUDE_CODE_REPO: 'https://github.com/cexll/myclaude/tree/master'
        }
      });

      await client.init();
    });

    afterAll(async () => {
      if (client) {
        await client.cleanup();
      }

      // 恢复备份的 .xclaude 目录
      if (await fs.pathExists('.xclaude-backup')) {
        await fs.move('.xclaude-backup', '.xclaude');
      }

      // 清理测试下载的目录
      if (await fs.pathExists('.xclaude')) {
        await fs.remove('.xclaude');
      }
    });

    test('should download and register tools from GitHub', async () => {
      // 等待服务器初始化和下载完成
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 检查是否下载了 .xclaude 目录
      expect(await fs.pathExists('.xclaude')).toBe(true);

      // 检查是否有 agents 和 commands 目录
      const hasAgents = await fs.pathExists('.xclaude/agents');
      const hasCommands = await fs.pathExists('.xclaude/commands');

      console.log(`Downloaded agents: ${hasAgents}, commands: ${hasCommands}`);

      // 至少应该有其中一个目录
      expect(hasAgents || hasCommands).toBe(true);
    });

    test('should register tools from downloaded content', async () => {
      try {
        const tools = await client.listTools();
        expect(tools).toBeDefined();
        expect(Array.isArray(tools)).toBe(true);

        const toolNames = tools.map(tool => tool.name);
        console.log('Registered tools:', toolNames);

        // 检查是否有注册的工具
        const hasSubAgents = toolNames.some(name => name.includes('-subagent'));
        const hasCommands = toolNames.some(name => name.includes('-command'));

        console.log(`Registered sub-agents: ${hasSubAgents}, commands: ${hasCommands}`);

        // 应该至少注册了一些工具
        expect(toolNames.length).toBeGreaterThan(0);
      } catch (error: any) {
        console.log('List tools error:', error.message);
        // 如果 listTools 不可用，至少验证服务器在运行
        expect(client).toBeDefined();
      }
    });

    test('should handle tool calls if tools are available', async () => {
      try {
        const tools = await client.listTools();

        if (tools && tools.length > 0) {
          const firstTool = tools[0];
          console.log(`Testing tool: ${firstTool.name}`);

          // 尝试调用第一个工具
          try {
            let result: any;
            if (firstTool.name.includes('-subagent')) {
              result = await client.callTool(firstTool.name, {
                task: 'Test task for integration testing'
              });
            } else if (firstTool.name.includes('-command')) {
              result = await client.callTool(firstTool.name, {
                arguments: 'test arguments'
              });
            } else {
              result = await client.callTool(firstTool.name, {});
            }

            expect(result).toBeDefined();
            if (result.content) {
              console.log('Tool call successful:', result.content[0]?.text?.substring(0, 100) + '...');
            } else {
              console.log('Tool call successful but no content returned');
            }
          } catch (error: any) {
            console.log('Tool call failed (may be expected):', error.message);
            // 工具调用失败可能是正常的，取决于工具的具体要求
          }
        } else {
          console.log('No tools available for testing');
        }
      } catch (error: any) {
        console.log('Tool testing error:', error.message);
      }
    });
  });

  async function setupTestXclaudeDirectory() {
    // 创建测试目录结构
    await fs.ensureDir(path.join(testXclaudeDir, 'subagents'));
    await fs.ensureDir(path.join(testXclaudeDir, 'commands'));

    // 创建测试 sub-agent
    const subAgentContent = `---
name: test-reviewer
description: Test code review sub-agent
tools: Read, Grep
---

You are a test code reviewer. When given a task, analyze the code and provide feedback.

Test instructions:
1. Review the provided code
2. Check for basic issues
3. Provide constructive feedback
`;

    await fs.writeFile(
      path.join(testXclaudeDir, 'subagents', 'test-reviewer.md'),
      subAgentContent
    );

    // 创建测试 command
    const commandContent = `# Test Command

This is a test command that processes arguments.

## Usage

Execute with: $ARGUMENTS

## Instructions

1. Process the input arguments
2. Return formatted output
3. Complete the task
`;

    await fs.writeFile(
      path.join(testXclaudeDir, 'commands', 'test-command.md'),
      commandContent
    );
  }

  test('should parse GitHub repository URLs correctly', () => {
    // 测试 GitHub URL 解析
    const testCases = [
      {
        input: 'cexll/myclaude',
        expected: { owner: 'cexll', repo: 'myclaude', branch: 'main' }
      },
      {
        input: 'https://github.com/cexll/myclaude',
        expected: { owner: 'cexll', repo: 'myclaude', branch: 'main' }
      },
      {
        input: 'https://github.com/owner/repo/tree/develop',
        expected: { owner: 'owner', repo: 'repo', branch: 'develop' }
      }
    ];

    testCases.forEach(({ input, expected }) => {
      const result = parseGitHubRepo(input);
      expect(result.owner).toBe(expected.owner);
      expect(result.repo).toBe(expected.repo);
      expect(result.branch).toBe(expected.branch);
    });
  });

  test('should process markdown files with frontmatter correctly', () => {
    // 测试 Markdown 文件处理
    const testFile = {
      name: 'test-agent.md',
      path: 'subagents/test-agent.md',
      content: `---
name: test-agent
description: Test agent for testing
tools: Read, Write
---

This is the system prompt for the test agent.

Instructions:
1. Do something
2. Do something else
`
    };

    const processed = processMarkdownFile(testFile);

    expect(processed.frontmatter).toBeDefined();
    expect(processed.frontmatter?.name).toBe('test-agent');
    expect(processed.frontmatter?.description).toBe('Test agent for testing');
    expect(processed.markdownContent).toContain('This is the system prompt');
    expect(processed.markdownContent).toContain('Instructions:');
  });

  test('should handle markdown files without frontmatter', () => {
    // 测试没有 frontmatter 的 Markdown 文件
    const testFile = {
      name: 'simple-command.md',
      path: 'commands/simple-command.md',
      content: `# Simple Command

This is a simple command without frontmatter.

Execute with: $ARGUMENTS

## Steps
1. Process input
2. Return result
`
    };

    const processed = processMarkdownFile(testFile);

    expect(processed.frontmatter).toEqual({});
    expect(processed.markdownContent).toContain('# Simple Command');
    expect(processed.markdownContent).toContain('$ARGUMENTS');
  });

  test('should convert .claude references to .xclaude', () => {
    // 测试 .claude 到 .xclaude 的转换
    const testFile = {
      name: 'test.md',
      path: 'test.md',
      content: `# Test File

This file references .claude directory and .claude files.

See .claude/subagents/ for more information.
Check the .claude configuration.
## Validation Criteria

### 1. **Template Structure Compliance**
- **Load and compare against template**: Use get-content script to read the requirements template:

~~~bash
# Windows:
claude-code-spec-workflow get-content "C:\path\to\project\.claude\templates\requirements-template.md"

# macOS/Linux:
claude-code-spec-workflow get-content "/path/to/project/.claude/templates/requirements-template.md"

- **Section validation**: Ensure all required template sections are present and non-empty
- **Format compliance**: Verify document follows exact template structure and formatting
- **Section order**: Check that sections appear in the correct template order
- **Missing sections**: Identify any template sections that are missing or incomplete
~~~
`
    };

    const processed = processMarkdownFile(testFile);

    expect(processed.content).toContain('.xclaude directory');
    expect(processed.content).toContain('.xclaude files');
    expect(processed.content).toContain('.xclaude/subagents/');
    expect(processed.content).toContain('.xclaude configuration');
    expect(processed.content).toContain('/path/to/project/.xclaude/');
    expect(processed.content).not.toContain('.claude');
  });

  test('should create test directory structure correctly', async () => {
    // 测试目录创建
    await setupTestXclaudeDirectory();

    // 验证目录结构
    expect(await fs.pathExists(testXclaudeDir)).toBe(true);
    expect(await fs.pathExists(path.join(testXclaudeDir, 'subagents'))).toBe(true);
    expect(await fs.pathExists(path.join(testXclaudeDir, 'commands'))).toBe(true);

    // 验证文件存在
    expect(await fs.pathExists(path.join(testXclaudeDir, 'subagents', 'test-reviewer.md'))).toBe(true);
    expect(await fs.pathExists(path.join(testXclaudeDir, 'commands', 'test-command.md'))).toBe(true);

    // 验证文件内容
    const subAgentContent = await fs.readFile(path.join(testXclaudeDir, 'subagents', 'test-reviewer.md'), 'utf-8');
    expect(subAgentContent).toContain('name: test-reviewer');
    expect(subAgentContent).toContain('You are a test code reviewer');

    const commandContent = await fs.readFile(path.join(testXclaudeDir, 'commands', 'test-command.md'), 'utf-8');
    expect(commandContent).toContain('# Test Command');
    expect(commandContent).toContain('$ARGUMENTS');
  });
});
