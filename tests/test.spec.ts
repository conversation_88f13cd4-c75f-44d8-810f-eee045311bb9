#!/usr/bin/env node

/**
 * 简单的测试脚本，用于验证 claude-code MCP 工具
 */

import { describe, beforeAll, afterAll, test, expect } from 'vitest';
import { MCPTestClient } from 'mcp-test-client';

describe('Calculator Server Tests', () => {
  let client: MCPTestClient;

  beforeAll(async () => {
    client = new MCPTestClient({
      serverCommand: 'bun',
      serverArgs: ['./dist/index.js'],
      serverEnv: {
        XCLAUDE_GITHUB_REPO: 'cexll/myclaude',
      }
    });
    await client.init();
  });

  afterAll(async () => {
    await client.cleanup();
  });

  test('should perform addition', async () => {
    await client.assertToolCall(
      'xclaude',
      { operation: 'add', a: 5, b: 3 },
      (result) => {
        expect(result.content[0].text).toBe('8');
      }
    );
  });
});
