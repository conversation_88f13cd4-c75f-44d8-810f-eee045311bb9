#!/usr/bin/env node

/**
 * 简单的测试脚本，用于验证 claude-code MCP 工具
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { registerClaudeCodeTool } from './dist/tools/claude-code.js';

async function testTool() {
  console.log('Testing Claude Code MCP Tool...');
  
  try {
    // 创建 MCP 服务器实例
    const server = new McpServer({
      name: 'test-claude-code-mcp',
      version: '1.0.0'
    });

    // 注册 claude-code 工具
    registerClaudeCodeTool(server);
    
    console.log('✅ Tool registered successfully!');
    console.log('🎉 Claude Code MCP Tool is ready to use!');
    
    console.log('\n📖 Usage examples:');
    console.log('1. use claude-code with githubRepo="cexll/myclaude"');
    console.log('2. use claude-code with githubRepo="https://github.com/cexll/myclaude" targetDir="custom-dir"');
    console.log('3. use claude-code with githubRepo="owner/repo" branch="develop"');
    
  } catch (error) {
    console.error('❌ Error testing tool:', error);
    process.exit(1);
  }
}

testTool();
