# Claude Code MCP 工具使用指南

## 快速开始

### 1. 安装和构建

```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 测试工具
node test-tool.js
```

### 2. 启动 MCP 服务器

```bash
npm start
```

## 使用方法

### 基本用法

#### 1. 使用 GitHub 仓库 URL

```
use claude-code with githubRepo="https://github.com/cexll/myclaude"
```

#### 2. 使用简短格式

```
use claude-code with githubRepo="cexll/myclaude"
```

#### 3. 指定目标目录和分支

```
use claude-code with githubRepo="cexll/myclaude" targetDir="my-claude-tools" branch="develop"
```

### 高级用法

#### 提示词引导方式

```
use claude-code to Build user management system with RBAC
```

这种方式会让大模型主动运用下载的 `.xclaude` 目录下的提示词，包括 sub-agents 和 slash-commands。

## 工具参数说明

| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| `githubRepo` | string | ✅ | - | GitHub 仓库 URL 或 owner/repo 格式 |
| `targetDir` | string | ❌ | `.xclaude` | 目标目录路径 |
| `branch` | string | ❌ | `main` | 分支名称 |

## 支持的仓库格式

- **完整 URL**: `https://github.com/cexll/myclaude`
- **简短格式**: `cexll/myclaude`
- **带分支**: `https://github.com/cexll/myclaude/tree/develop`

## 工作流程

1. **验证输入**: 检查 GitHub 仓库格式和参数安全性
2. **检查仓库**: 验证仓库是否存在和可访问
3. **下载文件**: 递归下载仓库中的所有文件
4. **本地存储**: 将文件保存到指定的目标目录
5. **工具注册**: 自动解析和注册 sub-agents 和 slash-commands

## 配置文件支持

### 方式一：配置文件

在仓库根目录创建 `config.json` 或 `claude-code.json`：

```json
{
  "subAgents": [
    {
      "name": "requirements-pilot",
      "description": "需求分析和项目规划助手",
      "prompt": "你是一个专业的需求分析师...",
      "filePath": "sub-agents/requirements-pilot.md"
    }
  ],
  "slashCommands": [
    {
      "name": "analyze",
      "description": "代码分析命令",
      "prompt": "请分析以下代码...",
      "filePath": "slash-commands/analyze.md"
    }
  ]
}
```

### 方式二：自动发现

如果没有配置文件，工具会自动发现仓库中的 Markdown 文件并注册为工具。

## 示例输出

```
Successfully downloaded 15 files and registered 8 tools from cexll/myclaude

Downloaded files:
.xclaude/README.md
.xclaude/sub-agents/requirements-pilot.md
.xclaude/slash-commands/analyze.md
...

Registered tools:
sub-agent:requirements-pilot
slash-command:analyze
auto:readme
...
```

## 错误处理

工具包含完善的错误处理：

- ❌ GitHub 仓库不存在或无法访问
- ❌ 网络连接问题
- ❌ 文件写入权限问题
- ❌ 无效的输入参数

## 安全特性

- ✅ 路径遍历攻击防护
- ✅ 输入参数验证
- ✅ GitHub API 错误处理
- ✅ 文件权限检查

## 故障排除

### 常见问题

1. **仓库无法访问**
   - 检查仓库是否为公开仓库
   - 验证仓库 URL 格式是否正确

2. **文件下载失败**
   - 检查网络连接
   - 确认 GitHub API 可访问

3. **权限错误**
   - 确保目标目录有写入权限
   - 检查文件系统空间

### 调试模式

启动时添加调试信息：

```bash
DEBUG=claude-code-mcp npm start
```
