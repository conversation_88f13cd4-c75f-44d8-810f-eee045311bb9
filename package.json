{"name": "claude-code-mcp", "version": "1.0.0", "description": "MCP tool for downloading and registering claude-code sub-agents and slash-commands from GitHub repositories", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "clean": "rm -rf dist", "prepare": "npm run build"}, "keywords": ["mcp", "claude-code", "github", "sub-agents", "slash-commands"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "fs-extra": "^11.2.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^20.0.0", "mcp-test-client": "^1.0.1", "typescript": "^5.0.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}