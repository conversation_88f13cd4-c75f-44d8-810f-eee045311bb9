{"name": "claude-code-mcp", "version": "1.0.0", "description": "MCP tool for downloading and registering claude-code sub-agents and slash-commands from GitHub repositories", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "test": "node test-tool.js", "clean": "rm -rf dist", "prepare": "npm run build"}, "keywords": ["mcp", "claude-code", "github", "sub-agents", "slash-commands"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "node-fetch": "^3.3.2", "fs-extra": "^11.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/fs-extra": "^11.0.4", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}