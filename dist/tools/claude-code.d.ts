/**
 * Claude Code MCP Tools - Register individual sub-agents and commands as MCP tools
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
/**
 * Initialize and register xclaude tools from local or remote sources
 * @param server - MCP server instance
 * @param githubRepo - Optional GitHub repository to download from if no local content
 */
export declare function initializeClaudeCodeTools(server: McpServer, githubRepo?: string): Promise<void>;
/**
 * Register claude-code tools with optional GitHub repository configuration
 * @param server - MCP server instance
 * @param githubRepo - Optional GitHub repository to download from if no local content
 */
export declare function registerClaudeCodeTool(server: McpServer, githubRepo?: string): Promise<void>;
//# sourceMappingURL=claude-code.d.ts.map