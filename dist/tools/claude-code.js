/**
 * Claude Code MCP Tools - Register individual sub-agents and commands as MCP tools
 */
import { z } from 'zod';
import path from 'path';
import { parseGitHubRepo, validateTargetDir } from '../utils/validation.js';
import { getAllFilesInDirectory, checkRepoExists } from '../utils/github.js';
import { writeFile, ensureDir, getFilesInDir, fileExists, readFile } from '../utils/file.js';
import { processMarkdownFile } from '../utils/markdown.js';
/**
 * Check and register tools from local .xclaude directory
 * @param server - MCP server instance
 * @param targetDir - Target directory path
 * @returns Whether local content was found and registered
 */
async function checkAndRegisterLocalTools(server, targetDir) {
    try {
        const xclaudeExists = await fileExists(targetDir);
        if (!xclaudeExists) {
            return false;
        }
        let hasContent = false;
        // Check for subagents or agents directory
        const subagentsDir = path.join(targetDir, 'subagents');
        const agentsDir = path.join(targetDir, 'agents');
        const subagentsExists = await fileExists(subagentsDir);
        const agentsExists = await fileExists(agentsDir);
        if (subagentsExists) {
            const subagentFiles = await getFilesInDir(subagentsDir);
            for (const filePath of subagentFiles) {
                if (filePath.endsWith('.md')) {
                    await registerSubAgentFromFile(server, filePath);
                    hasContent = true;
                }
            }
        }
        if (agentsExists) {
            const agentFiles = await getFilesInDir(agentsDir);
            for (const filePath of agentFiles) {
                if (filePath.endsWith('.md')) {
                    await registerSubAgentFromFile(server, filePath);
                    hasContent = true;
                }
            }
        }
        // Check for commands directory
        const commandsDir = path.join(targetDir, 'commands');
        const commandsExists = await fileExists(commandsDir);
        if (commandsExists) {
            const commandFiles = await getFilesInDir(commandsDir);
            for (const filePath of commandFiles) {
                if (filePath.endsWith('.md')) {
                    await registerSlashCommandFromFile(server, filePath);
                    hasContent = true;
                }
            }
        }
        return hasContent;
    }
    catch (error) {
        console.warn(`Failed to check local .xclaude directory: ${error}`);
        return false;
    }
}
/**
 * Download from GitHub and register tools
 * @param server - MCP server instance
 * @param githubRepo - GitHub repository
 * @param targetDir - Target directory
 */
async function downloadAndRegisterFromGitHub(server, githubRepo, targetDir) {
    // Validate input parameters
    const repoInfo = parseGitHubRepo(githubRepo);
    // Use the branch from URL parsing, or default to 'main'
    if (!repoInfo.branch) {
        repoInfo.branch = 'main';
    }
    const safeTargetDir = validateTargetDir(targetDir);
    // Check if repository exists
    const repoExists = await checkRepoExists(repoInfo);
    if (!repoExists) {
        throw new Error(`Repository ${repoInfo.owner}/${repoInfo.repo} not found or not accessible`);
    }
    // Download repository contents
    const files = await getAllFilesInDirectory(repoInfo);
    if (files.length === 0) {
        throw new Error(`No files found in repository ${repoInfo.owner}/${repoInfo.repo}`);
    }
    // Ensure target directory exists
    await ensureDir(safeTargetDir);
    // Save files locally
    for (const file of files) {
        const localPath = path.join(safeTargetDir, file.path);
        await writeFile(localPath, file.content);
    }
    // Register tools from downloaded files
    await registerToolsFromFiles(server, files);
}
/**
 * Register tools from downloaded files
 * @param server - MCP server instance
 * @param files - Downloaded files
 */
async function registerToolsFromFiles(server, files) {
    const markdownFiles = files.filter(f => f.name.endsWith('.md'));
    for (const file of markdownFiles) {
        const processedFile = processMarkdownFile(file);
        // Determine if it's a sub-agent or slash command based on path
        if (file.path.includes('/subagents/') || file.path.includes('\\subagents\\') ||
            file.path.includes('/agents/') || file.path.includes('\\agents\\')) {
            await registerSubAgentFromProcessedFile(server, processedFile);
        }
        else if (file.path.includes('/commands/') || file.path.includes('\\commands\\')) {
            await registerSlashCommandFromProcessedFile(server, processedFile);
        }
    }
}
/**
 * Initialize and register xclaude tools from local or remote sources
 * @param server - MCP server instance
 * @param githubRepo - Optional GitHub repository to download from if no local content
 */
export async function initializeClaudeCodeTools(server, githubRepo) {
    const targetDir = '.xclaude';
    try {
        // Check if local .xclaude directory exists and has content
        const hasLocalContent = await checkAndRegisterLocalTools(server, targetDir);
        if (hasLocalContent) {
            console.log('Registered tools from local .xclaude directory');
            return;
        }
        // If no local content and githubRepo is provided, download from GitHub
        if (githubRepo) {
            console.log(`No local .xclaude directory found. Downloading from ${githubRepo}...`);
            await downloadAndRegisterFromGitHub(server, githubRepo, targetDir);
            console.log('Successfully downloaded and registered tools from GitHub');
            return;
        }
        console.log('No local .xclaude directory found and no GitHub repository configured.');
    }
    catch (error) {
        console.error(`Failed to initialize xclaude tools: ${error}`);
    }
}
/**
 * Register sub-agent from file path
 * @param server - MCP server instance
 * @param filePath - File path
 */
async function registerSubAgentFromFile(server, filePath) {
    try {
        const content = await readFile(filePath);
        const processedFile = processMarkdownFile({
            name: path.basename(filePath),
            path: filePath,
            content
        });
        await registerSubAgentFromProcessedFile(server, processedFile);
    }
    catch (error) {
        console.warn(`Failed to register sub-agent from ${filePath}: ${error}`);
    }
}
/**
 * Register slash command from file path
 * @param server - MCP server instance
 * @param filePath - File path
 */
async function registerSlashCommandFromFile(server, filePath) {
    try {
        const content = await readFile(filePath);
        const processedFile = processMarkdownFile({
            name: path.basename(filePath),
            path: filePath,
            content
        });
        await registerSlashCommandFromProcessedFile(server, processedFile);
    }
    catch (error) {
        console.warn(`Failed to register slash command from ${filePath}: ${error}`);
    }
}
/**
 * Register sub-agent from processed markdown file
 * @param server - MCP server instance
 * @param file - Processed markdown file
 */
async function registerSubAgentFromProcessedFile(server, file) {
    const { frontmatter, markdownContent } = file;
    if (!frontmatter || !frontmatter.name || !frontmatter.description) {
        console.warn(`Sub-agent file ${file.path} missing required frontmatter fields`);
        return;
    }
    const toolName = `${frontmatter.name}-subagent`;
    server.registerTool(toolName, {
        title: `Sub-agent: ${frontmatter.name}`,
        description: frontmatter.description,
        inputSchema: {
            task: z.string().describe('Task description for the sub-agent')
        }
    }, async (args) => {
        const { task } = args;
        return {
            content: [{
                    type: 'text',
                    text: `Executing sub-agent ${frontmatter.name} with task: ${task}\n\nSystem Prompt:\n${markdownContent}`
                }]
        };
    });
}
/**
 * Register slash command from processed markdown file
 * @param server - MCP server instance
 * @param file - Processed markdown file
 */
async function registerSlashCommandFromProcessedFile(server, file) {
    const { frontmatter, markdownContent } = file;
    const name = file.name.replace('.md', '');
    const description = (frontmatter?.description) || markdownContent.split('\n')[0] || `Slash command: ${name}`;
    const toolName = `${name}-command`;
    server.registerTool(toolName, {
        title: `Command: ${name}`,
        description,
        inputSchema: {
            arguments: z.string().optional().describe('Arguments for the command (replaces $ARGUMENTS in content)')
        }
    }, async (args) => {
        const { arguments: cmdArgs = '' } = args;
        // Replace $ARGUMENTS placeholder with actual arguments
        const processedContent = markdownContent.replace(/\$ARGUMENTS/g, cmdArgs);
        return {
            content: [{
                    type: 'text',
                    text: `Executing command ${name}${cmdArgs ? ` with arguments: ${cmdArgs}` : ''}\n\nContent:\n${processedContent}`
                }]
        };
    });
}
//# sourceMappingURL=claude-code.js.map