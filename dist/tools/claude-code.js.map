{"version": 3, "file": "claude-code.js", "sourceRoot": "", "sources": ["../../src/tools/claude-code.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC5E,OAAO,EAAE,sBAAsB,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAC7E,OAAO,EAAE,SAAS,EAAE,SAAS,EAAiB,MAAM,kBAAkB,CAAC;AAIvE;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CAAC,MAAiB;IACtD,MAAM,CAAC,YAAY,CACjB,aAAa,EACb;QACE,KAAK,EAAE,kBAAkB;QACzB,WAAW,EAAE;;;;;;;;;;;mCAWgB;QAC7B,WAAW,EAAE;YACX,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sFAAsF,CAAC;YACvH,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC;YAChE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;SACxD;KACF,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;QACb,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,SAAS,GAAG,UAAU,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,IAA0B,CAAC;YAE3F,SAAS;YACT,MAAM,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;YAC7C,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;YACzB,MAAM,aAAa,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEnD,WAAW;YACX,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,qBAAqB,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,8BAA8B;yBACzF,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAED,SAAS;YACT,MAAM,KAAK,GAAG,MAAM,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,uCAAuC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE;yBAC/E,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAED,WAAW;YACX,MAAM,SAAS,CAAC,aAAa,CAAC,CAAC;YAE/B,UAAU;YACV,MAAM,eAAe,GAAa,EAAE,CAAC;YACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtD,MAAM,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC;YAED,UAAU;YACV,MAAM,eAAe,GAAG,MAAM,qBAAqB,CAAC,MAAM,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YAElF,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,2BAA2B,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,MAAM,eAAe,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,0BAA0B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;qBAC/P,CAAC;aACH,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,6CAA6C,KAAK,EAAE;qBAC3D,CAAC;gBACF,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,KAAK,UAAU,qBAAqB,CAClC,MAAiB,EACjB,SAAiB,EACjB,KAA0B;IAE1B,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,IAAI,CAAC;QACH,SAAS;QACT,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAkB,CAAC,CAAC;QAE9F,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB;YAChB,MAAM,MAAM,GAAqB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEhE,gBAAgB;YAChB,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,SAAS,IAAI,EAAE,EAAE,CAAC;gBAC3C,MAAM,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACtC,eAAe,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAClD,CAAC;YAED,oBAAoB;YACpB,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,aAAa,IAAI,EAAE,EAAE,CAAC;gBACjD,MAAM,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC5C,eAAe,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,mBAAmB;YACnB,MAAM,cAAc,GAAG,MAAM,uBAAuB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACpE,eAAe,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QAC1C,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,gBAAgB,CAAC,MAAiB,EAAE,KAAe;IAChE,MAAM,CAAC,YAAY,CACjB,aAAa,KAAK,CAAC,IAAI,EAAE,EACzB;QACE,KAAK,EAAE,cAAc,KAAK,CAAC,IAAI,EAAE;QACjC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,cAAc,KAAK,CAAC,IAAI,EAAE;QAC5D,WAAW,EAAE;YACX,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;SAChE;KACF,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;QACb,MAAM,EAAE,IAAI,EAAE,GAAG,IAAwB,CAAC;QAC1C,OAAO;YACL,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,uBAAuB,KAAK,CAAC,IAAI,eAAe,IAAI,eAAe,KAAK,CAAC,MAAM,EAAE;iBACxF,CAAC;SACH,CAAC;IACJ,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,oBAAoB,CAAC,MAAiB,EAAE,OAAqB;IAC1E,MAAM,CAAC,YAAY,CACjB,SAAS,OAAO,CAAC,IAAI,EAAE,EACvB;QACE,KAAK,EAAE,kBAAkB,OAAO,CAAC,IAAI,EAAE;QACvC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,kBAAkB,OAAO,CAAC,IAAI,EAAE;QACpE,WAAW,EAAE;YACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;SAC1D;KACF,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;QACb,MAAM,EAAE,KAAK,EAAE,GAAG,IAAyB,CAAC;QAC5C,OAAO;YACL,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,2BAA2B,OAAO,CAAC,IAAI,gBAAgB,KAAK,eAAe,OAAO,CAAC,MAAM,EAAE;iBAClG,CAAC;SACH,CAAC;IACJ,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,KAAK,UAAU,uBAAuB,CAAC,MAAiB,EAAE,KAA0B;IAClF,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,2BAA2B;IAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAEhE,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;QAE5D,MAAM,CAAC,YAAY,CACjB,QAAQ,QAAQ,EAAE,EAClB;YACE,KAAK,EAAE,oBAAoB,QAAQ,EAAE;YACrC,WAAW,EAAE,6BAA6B,IAAI,CAAC,IAAI,EAAE;YACrD,WAAW,EAAE;gBACX,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;aACjE;SACF,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;YACb,MAAM,EAAE,KAAK,EAAE,GAAG,IAAyB,CAAC;YAC5C,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,kCAAkC,QAAQ,gBAAgB,KAAK,oBAAoB,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE;qBACvH,CAAC;aACH,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,eAAe,CAAC,IAAI,CAAC,QAAQ,QAAQ,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC"}