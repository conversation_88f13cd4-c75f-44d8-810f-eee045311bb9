{"version": 3, "file": "claude-code.js", "sourceRoot": "", "sources": ["../../src/tools/claude-code.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAa,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAClF,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAI3D;;;;;GAKG;AACH,KAAK,UAAU,0BAA0B,CAAC,MAAiB,EAAE,SAAiB;IAC5E,IAAI,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,6BAA6B;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,YAAY,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,SAAS,CAAC,CAAC;YAClD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAClC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,MAAM,qBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;oBAC9C,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;YACH,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QACrD,MAAM,cAAc,GAAG,MAAM,UAAU,CAAC,WAAW,CAAC,CAAC;QAErD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,YAAY,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;gBACpC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,MAAM,uBAAuB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;oBAChD,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,6CAA6C,KAAK,EAAE,CAAC,CAAC;QACnE,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,KAAK,UAAU,6BAA6B,CAAC,MAAiB,EAAE,UAAkB,EAAE,SAAiB;IACnG,4BAA4B;IAC5B,MAAM,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;IAC7C,MAAM,aAAa,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAEnD,sCAAsC;IACtC,MAAM,KAAK,GAAG,MAAM,eAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAE7D,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9F,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,CAAC,MAAM,eAAe,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IAExF,uCAAuC;IACvC,MAAM,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC9C,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,sBAAsB,CAAC,MAAiB,EAAE,KAA0B;IACjF,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAEhE,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,MAAM,aAAa,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAEhD,sDAAsD;QACtD,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACvE,MAAM,8BAA8B,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAClF,MAAM,gCAAgC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAAC,MAAiB,EAAE,UAAmB;IACpF,MAAM,SAAS,GAAG,UAAU,CAAC;IAE7B,IAAI,CAAC;QACH,2DAA2D;QAC3D,MAAM,eAAe,GAAG,MAAM,0BAA0B,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE5E,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAC9D,OAAO;QACT,CAAC;QAED,uEAAuE;QACvE,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,uDAAuD,UAAU,KAAK,CAAC,CAAC;YACpF,MAAM,6BAA6B,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;YACxE,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wEAAwE,CAAC,CAAC;IACxF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,qBAAqB,CAAC,MAAiB,EAAE,QAAgB;IACtE,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,mBAAmB,CAAC;YACxC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC7B,IAAI,EAAE,QAAQ;YACd,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,8BAA8B,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,iCAAiC,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;IACtE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,uBAAuB,CAAC,MAAiB,EAAE,QAAgB;IACxE,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,mBAAmB,CAAC;YACxC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC7B,IAAI,EAAE,QAAQ;YACd,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,gCAAgC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAChE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,mCAAmC,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;IACxE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,8BAA8B,CAAC,MAAiB,EAAE,IAAkB;IACjF,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;IAE9C,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAClE,OAAO,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,sCAAsC,CAAC,CAAC;QAC5E,OAAO;IACT,CAAC;IAED,MAAM,QAAQ,GAAG,GAAG,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE7C,MAAM,CAAC,YAAY,CACjB,QAAQ,EACR;QACE,KAAK,EAAE,UAAU,WAAW,CAAC,IAAI,EAAE;QACnC,WAAW,EAAE,WAAW,CAAC,WAAW;QACpC,WAAW,EAAE;YACX,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gCAAgC,CAAC;SAC5D;KACF,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;QACb,MAAM,EAAE,IAAI,EAAE,GAAG,IAAwB,CAAC;QAC1C,OAAO;YACL,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,mBAAmB,WAAW,CAAC,IAAI,eAAe,IAAI,uBAAuB,eAAe,EAAE;iBACrG,CAAC;SACH,CAAC;IACJ,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,gCAAgC,CAAC,MAAiB,EAAE,IAAkB;IACnF,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;IAE9C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,IAAI,EAAE,CAAC;IAEvG,MAAM,QAAQ,GAAG,GAAG,IAAI,UAAU,CAAC;IAEnC,MAAM,CAAC,YAAY,CACjB,QAAQ,EACR;QACE,KAAK,EAAE,YAAY,IAAI,EAAE;QACzB,WAAW;QACX,WAAW,EAAE;YACX,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4DAA4D,CAAC;SACxG;KACF,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;QACb,MAAM,EAAE,SAAS,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,IAA8B,CAAC;QAEnE,uDAAuD;QACvD,MAAM,gBAAgB,GAAG,eAAe,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAE1E,OAAO;YACL,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,qBAAqB,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,iBAAiB,gBAAgB,EAAE;iBAClH,CAAC;SACH,CAAC;IACJ,CAAC,CACF,CAAC;AACJ,CAAC"}