#!/usr/bin/env node
/**
 * Claude Code MCP 工具主入口文件
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { registerClaudeCodeTool } from './tools/claude-code.js';
/**
 * 创建并启动 MCP 服务器
 */
async function createServer() {
    // 创建 MCP 服务器实例
    const server = new McpServer({
        name: 'xclaude',
        version: '1.0.0'
    });
    // Register claude-code tools with optional GitHub repository
    // You can configure a default GitHub repository here
    const defaultGitHubRepo = process.env.CLAUDE_CODE_REPO; // e.g., "cexll/myclaude"
    await registerClaudeCodeTool(server, defaultGitHubRepo);
    return server;
}
/**
 * Start the MCP server
 */
export async function startServer() {
    try {
        const server = await createServer();
        // 创建 stdio 传输层
        const transport = new StdioServerTransport();
        // 启动服务器
        await server.connect(transport);
        // 服务器启动成功，但不要在 stdio 模式下输出到 console
        // 因为这会干扰 MCP 协议通信
    }
    catch (error) {
        console.error('Failed to start MCP server:', error);
        process.exit(1);
    }
}
// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// 启动服务器
startServer().catch((error) => {
    console.error('Failed to start MCP server:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map