/**
 * MCP 工具相关类型定义
 */
export interface ClaudeCodeToolArgs {
    githubRepo: string;
    targetDir?: string;
    branch?: string;
}
export interface ToolRegistrationResult {
    success: boolean;
    message: string;
    downloadedFiles?: string[];
    registeredTools?: string[];
    error?: string;
}
export interface McpToolDefinition {
    name: string;
    description: string;
    inputSchema: {
        type: string;
        properties: Record<string, any>;
        required?: string[];
    };
}
//# sourceMappingURL=mcp.d.ts.map