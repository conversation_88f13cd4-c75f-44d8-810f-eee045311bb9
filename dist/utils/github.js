/**
 * GitHub API 相关工具函数
 */
import fetch from 'node-fetch';
/**
 * GitHub API 基础 URL
 */
const GITHUB_API_BASE = 'https://api.github.com';
/**
 * 获取 GitHub 仓库内容
 * @param repoInfo - 仓库信息
 * @param path - 文件或目录路径
 * @returns 文件内容数组
 */
export async function getGitHubRepoContents(repoInfo, path = '') {
    const { owner, repo, branch = 'main' } = repoInfo;
    const url = `${GITHUB_API_BASE}/repos/${owner}/${repo}/contents/${path}?ref=${branch}`;
    try {
        const response = await fetch(url);
        if (!response.ok) {
            if (response.status === 404) {
                throw new Error(`Repository ${owner}/${repo} not found or path ${path} does not exist`);
            }
            throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        const items = Array.isArray(data) ? data : [data];
        return items.map(item => ({
            name: item.name,
            path: item.path,
            content: item.content ? Buffer.from(item.content, 'base64').toString('utf-8') : '',
            type: item.type,
            download_url: item.download_url || undefined
        }));
    }
    catch (error) {
        throw new Error(`Failed to fetch GitHub repository contents: ${error}`);
    }
}
/**
 * 下载单个文件内容
 * @param downloadUrl - 文件下载 URL
 * @returns 文件内容
 */
export async function downloadFileContent(downloadUrl) {
    try {
        const response = await fetch(downloadUrl);
        if (!response.ok) {
            throw new Error(`Failed to download file: ${response.status} ${response.statusText}`);
        }
        return await response.text();
    }
    catch (error) {
        throw new Error(`Failed to download file content: ${error}`);
    }
}
/**
 * 递归获取目录下的所有文件
 * @param repoInfo - 仓库信息
 * @param dirPath - 目录路径
 * @returns 所有文件内容
 */
export async function getAllFilesInDirectory(repoInfo, dirPath = '') {
    const allFiles = [];
    try {
        const contents = await getGitHubRepoContents(repoInfo, dirPath);
        for (const item of contents) {
            if (item.type === 'file') {
                // 如果是文件，下载内容
                if (item.download_url) {
                    item.content = await downloadFileContent(item.download_url);
                }
                allFiles.push(item);
            }
            else if (item.type === 'dir') {
                // 如果是目录，递归获取子文件
                const subFiles = await getAllFilesInDirectory(repoInfo, item.path);
                allFiles.push(...subFiles);
            }
        }
    }
    catch (error) {
        throw new Error(`Failed to get all files in directory ${dirPath}: ${error}`);
    }
    return allFiles;
}
/**
 * 检查仓库是否存在
 * @param repoInfo - 仓库信息
 * @returns 仓库是否存在
 */
export async function checkRepoExists(repoInfo) {
    const { owner, repo } = repoInfo;
    const url = `${GITHUB_API_BASE}/repos/${owner}/${repo}`;
    try {
        const response = await fetch(url);
        return response.ok;
    }
    catch {
        return false;
    }
}
//# sourceMappingURL=github.js.map