/**
 * 输入验证工具函数
 */
/**
 * 验证 GitHub 仓库 URL 或仓库名称格式
 * @param repoInput - GitHub 仓库 URL 或 owner/repo 格式
 * @returns 解析后的仓库信息
 */
export function parseGitHubRepo(repoInput) {
    // 移除前后空格
    const input = repoInput.trim();
    // 处理完整的 GitHub URL
    const urlPattern = /^https?:\/\/github\.com\/([^\/]+)\/([^\/]+)(?:\/tree\/([^\/]+))?/;
    const urlMatch = input.match(urlPattern);
    if (urlMatch) {
        return {
            owner: urlMatch[1],
            repo: urlMatch[2].replace(/\.git$/, ''), // 移除 .git 后缀
            branch: urlMatch[3] || 'main'
        };
    }
    // 处理 owner/repo 格式
    const repoPattern = /^([^\/]+)\/([^\/]+)$/;
    const repoMatch = input.match(repoPattern);
    if (repoMatch) {
        return {
            owner: repoMatch[1],
            repo: repoMatch[2],
            branch: 'main'
        };
    }
    throw new Error(`Invalid GitHub repository format: ${input}. Expected format: "owner/repo" or "https://github.com/owner/repo"`);
}
/**
 * 验证路径安全性，防止路径遍历攻击
 * @param path - 要验证的路径
 * @returns 是否为安全路径
 */
export function isPathSafe(path) {
    // 检查是否包含危险的路径遍历字符
    const dangerousPatterns = [
        /\.\./, // 父目录引用
        /^\/+/, // 绝对路径
        /~\//, // 用户主目录
        /\$\{/, // 环境变量
        /\%/, // URL 编码
    ];
    return !dangerousPatterns.some(pattern => pattern.test(path));
}
/**
 * 验证目标目录路径
 * @param targetDir - 目标目录路径
 * @returns 标准化后的安全路径
 */
export function validateTargetDir(targetDir) {
    if (!targetDir) {
        return '.xclaude';
    }
    // 标准化路径
    const normalizedPath = targetDir.replace(/\\/g, '/').replace(/\/+/g, '/');
    if (!isPathSafe(normalizedPath)) {
        throw new Error(`Unsafe target directory path: ${targetDir}`);
    }
    return normalizedPath;
}
//# sourceMappingURL=validation.js.map