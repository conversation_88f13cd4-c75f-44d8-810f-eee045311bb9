/**
 * 输入验证工具函数
 */
import { GitHubRepoInfo } from '../types/github.js';
/**
 * 验证 GitHub 仓库 URL 或仓库名称格式
 * @param repoInput - GitHub 仓库 URL 或 owner/repo 格式
 * @returns 解析后的仓库信息
 */
export declare function parseGitHubRepo(repoInput: string): GitHubRepoInfo;
/**
 * 验证路径安全性，防止路径遍历攻击
 * @param path - 要验证的路径
 * @returns 是否为安全路径
 */
export declare function isPathSafe(path: string): boolean;
/**
 * 验证目标目录路径
 * @param targetDir - 目标目录路径
 * @returns 标准化后的安全路径
 */
export declare function validateTargetDir(targetDir: string): string;
//# sourceMappingURL=validation.d.ts.map