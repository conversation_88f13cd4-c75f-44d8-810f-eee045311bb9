/**
 * 文件操作工具函数
 */
/**
 * 确保目录存在，如果不存在则创建
 * @param dirPath - 目录路径
 */
export declare function ensureDir(dirPath: string): Promise<void>;
/**
 * 写入文件内容
 * @param filePath - 文件路径
 * @param content - 文件内容
 */
export declare function writeFile(filePath: string, content: string): Promise<void>;
/**
 * 读取文件内容
 * @param filePath - 文件路径
 * @returns 文件内容
 */
export declare function readFile(filePath: string): Promise<string>;
/**
 * 检查文件是否存在
 * @param filePath - 文件路径
 * @returns 文件是否存在
 */
export declare function fileExists(filePath: string): Promise<boolean>;
/**
 * 获取目录下的所有文件
 * @param dirPath - 目录路径
 * @param recursive - 是否递归获取子目录文件
 * @returns 文件路径数组
 */
export declare function getFilesInDir(dirPath: string, recursive?: boolean): Promise<string[]>;
//# sourceMappingURL=file.d.ts.map