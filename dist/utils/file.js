/**
 * 文件操作工具函数
 */
import fs from 'fs-extra';
import path from 'path';
/**
 * 确保目录存在，如果不存在则创建
 * @param dirPath - 目录路径
 */
export async function ensureDir(dirPath) {
    try {
        await fs.ensureDir(dirPath);
    }
    catch (error) {
        throw new Error(`Failed to create directory ${dirPath}: ${error}`);
    }
}
/**
 * 写入文件内容
 * @param filePath - 文件路径
 * @param content - 文件内容
 */
export async function writeFile(filePath, content) {
    try {
        // 确保父目录存在
        const dir = path.dirname(filePath);
        await ensureDir(dir);
        // 写入文件
        await fs.writeFile(filePath, content, 'utf-8');
    }
    catch (error) {
        throw new Error(`Failed to write file ${filePath}: ${error}`);
    }
}
/**
 * 读取文件内容
 * @param filePath - 文件路径
 * @returns 文件内容
 */
export async function readFile(filePath) {
    try {
        return await fs.readFile(filePath, 'utf-8');
    }
    catch (error) {
        throw new Error(`Failed to read file ${filePath}: ${error}`);
    }
}
/**
 * 检查文件是否存在
 * @param filePath - 文件路径
 * @returns 文件是否存在
 */
export async function fileExists(filePath) {
    try {
        await fs.access(filePath);
        return true;
    }
    catch {
        return false;
    }
}
/**
 * 获取目录下的所有文件
 * @param dirPath - 目录路径
 * @param recursive - 是否递归获取子目录文件
 * @returns 文件路径数组
 */
export async function getFilesInDir(dirPath, recursive = true) {
    const files = [];
    try {
        const items = await fs.readdir(dirPath);
        for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stat = await fs.stat(itemPath);
            if (stat.isFile()) {
                files.push(itemPath);
            }
            else if (stat.isDirectory() && recursive) {
                const subFiles = await getFilesInDir(itemPath, recursive);
                files.push(...subFiles);
            }
        }
    }
    catch (error) {
        throw new Error(`Failed to read directory ${dirPath}: ${error}`);
    }
    return files;
}
//# sourceMappingURL=file.js.map