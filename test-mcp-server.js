#!/usr/bin/env node

/**
 * 简单的 MCP 服务器测试脚本
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testMCPServer() {
  console.log('🚀 Testing MCP Server...');

  // 启动 MCP 服务器
  const serverProcess = spawn('node', [join(__dirname, 'dist/index.js')], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let serverOutput = '';
  let serverError = '';

  serverProcess.stdout.on('data', (data) => {
    serverOutput += data.toString();
  });

  serverProcess.stderr.on('data', (data) => {
    serverError += data.toString();
    console.error('Server stderr:', data.toString());
  });

  // 等待服务器启动和工具注册完成
  await new Promise((resolve) => setTimeout(resolve, 10000));

  // 发送 MCP 初始化请求
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };

  console.log('📤 Sending initialize request...');
  serverProcess.stdin.write(JSON.stringify(initRequest) + '\n');

  // 等待响应
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 发送 tools/list 请求
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  console.log('📤 Sending tools/list request...');
  serverProcess.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  // 等待响应
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // 发送 tools/call 请求
  const callToolRequest = {
    jsonrpc: '2.0',
    id: 3,
    method: 'tools/call',
    params: {
      name: 'claude-code-status',
      arguments: {}
    }
  };

  console.log('📤 Sending tools/call request...');
  serverProcess.stdin.write(JSON.stringify(callToolRequest) + '\n');

  // 等待响应
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // 关闭服务器
  serverProcess.kill();

  console.log('📥 Server output:', serverOutput);
  if (serverError) {
    console.error('❌ Server errors:', serverError);
  }

  console.log('✅ Test completed');
}

testMCPServer().catch(console.error);
