/**
 * Claude Code MCP 工具核心实现
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import path from 'path';
import { parseGitHubRepo, validateTargetDir } from '../utils/validation.js';
import { getAllFilesInDirectory, checkRepoExists } from '../utils/github.js';
import { writeFile, ensureDir, getFilesInDir } from '../utils/file.js';
import { ClaudeCodeToolArgs, ToolRegistrationResult } from '../types/mcp.js';
import { GitHubFileContent, ClaudeCodeConfig, SubAgent, SlashCommand } from '../types/github.js';

/**
 * 注册 claude-code MCP 工具
 * @param server - MCP 服务器实例
 */
export function registerClaudeCodeTool(server: McpServer): void {
  server.registerTool(
    'claude-code',
    {
      title: 'Claude Code Tool',
      description: `Universal Demand-Driven Development Workflow Orchestrator.

      通过接受 github-repo 参数，自动下载存储在项目根目录下的 .xclaude 目录下的 claude-code sub-agents 和 slash-commands，
      然后注册相关工具，实现类似提示词引导的方式。

      使用方式：通过明确的提示例如 "use claude-code to Build user management system with RBAC"，
      让大模型去主动运用 .xclaude 下的提示词，比如 sub-agents and slash-commands。

      参数：
      - githubRepo: GitHub 仓库 URL 或 owner/repo 格式 (必需)
      - targetDir: 目标目录，默认为 .xclaude (可选)
      - branch: 分支名称，默认为 main (可选)`,
      inputSchema: {
        githubRepo: z.string().describe('GitHub 仓库 URL 或 owner/repo 格式，例如: https://github.com/cexll/myclaude 或 cexll/myclaude'),
        targetDir: z.string().optional().describe('目标目录路径，默认为 .xclaude'),
        branch: z.string().optional().describe('分支名称，默认为 main')
      }
    },
    async (args) => {
      try {
        const { githubRepo, targetDir = '.xclaude', branch = 'main' } = args as ClaudeCodeToolArgs;

        // 验证输入参数
        const repoInfo = parseGitHubRepo(githubRepo);
        repoInfo.branch = branch;
        const safeTargetDir = validateTargetDir(targetDir);

        // 检查仓库是否存在
        const repoExists = await checkRepoExists(repoInfo);
        if (!repoExists) {
          return {
            content: [{
              type: 'text',
              text: `Error: Repository ${repoInfo.owner}/${repoInfo.repo} not found or not accessible`
            }],
            isError: true
          };
        }

        // 下载仓库内容
        const files = await getAllFilesInDirectory(repoInfo);

        if (files.length === 0) {
          return {
            content: [{
              type: 'text',
              text: `Error: No files found in repository ${repoInfo.owner}/${repoInfo.repo}`
            }],
            isError: true
          };
        }

        // 确保目标目录存在
        await ensureDir(safeTargetDir);

        // 保存文件到本地
        const downloadedFiles: string[] = [];
        for (const file of files) {
          const localPath = path.join(safeTargetDir, file.path);
          await writeFile(localPath, file.content);
          downloadedFiles.push(localPath);
        }

        // 解析和注册工具
        const registeredTools = await parseAndRegisterTools(server, safeTargetDir, files);

        return {
          content: [{
            type: 'text',
            text: `Successfully downloaded ${downloadedFiles.length} files and registered ${registeredTools.length} tools from ${repoInfo.owner}/${repoInfo.repo}\n\nDownloaded files:\n${downloadedFiles.join('\n')}\n\nRegistered tools:\n${registeredTools.join('\n')}`
          }]
        };

      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Failed to process claude-code repository: ${error}`
          }],
          isError: true
        };
      }
    }
  );
}

/**
 * 解析并注册工具
 * @param server - MCP 服务器实例
 * @param targetDir - 目标目录
 * @param files - 下载的文件列表
 * @returns 注册的工具名称列表
 */
async function parseAndRegisterTools(
  server: McpServer,
  targetDir: string,
  files: GitHubFileContent[]
): Promise<string[]> {
  const registeredTools: string[] = [];

  try {
    // 查找配置文件
    const configFile = files.find(f => f.name === 'config.json' || f.name === 'claude-code.json');

    if (configFile) {
      // 如果有配置文件，按配置注册
      const config: ClaudeCodeConfig = JSON.parse(configFile.content);

      // 注册 sub-agents
      for (const agent of config.subAgents || []) {
        await registerSubAgent(server, agent);
        registeredTools.push(`sub-agent:${agent.name}`);
      }

      // 注册 slash-commands
      for (const command of config.slashCommands || []) {
        await registerSlashCommand(server, command);
        registeredTools.push(`slash-command:${command.name}`);
      }
    } else {
      // 如果没有配置文件，自动发现和注册
      const autoRegistered = await autoDiscoverAndRegister(server, files);
      registeredTools.push(...autoRegistered);
    }

  } catch (error) {
    console.warn(`Failed to parse and register tools: ${error}`);
  }

  return registeredTools;
}

/**
 * 注册 sub-agent
 * @param server - MCP 服务器实例
 * @param agent - sub-agent 配置
 */
async function registerSubAgent(server: McpServer, agent: SubAgent): Promise<void> {
  server.registerTool(
    `sub-agent-${agent.name}`,
    {
      title: `Sub-agent: ${agent.name}`,
      description: agent.description || `Sub-agent: ${agent.name}`,
      inputSchema: {
        task: z.string().describe('Task description for the sub-agent')
      }
    },
    async (args) => {
      const { task } = args as { task: string };
      return {
        content: [{
          type: 'text',
          text: `Executing sub-agent ${agent.name} with task: ${task}\n\nPrompt: ${agent.prompt}`
        }]
      };
    }
  );
}

/**
 * 注册 slash-command
 * @param server - MCP 服务器实例
 * @param command - slash-command 配置
 */
async function registerSlashCommand(server: McpServer, command: SlashCommand): Promise<void> {
  server.registerTool(
    `slash-${command.name}`,
    {
      title: `Slash command: ${command.name}`,
      description: command.description || `Slash command: ${command.name}`,
      inputSchema: {
        input: z.string().describe('Input for the slash command')
      }
    },
    async (args) => {
      const { input } = args as { input: string };
      return {
        content: [{
          type: 'text',
          text: `Executing slash command ${command.name} with input: ${input}\n\nPrompt: ${command.prompt}`
        }]
      };
    }
  );
}

/**
 * 自动发现和注册工具
 * @param server - MCP 服务器实例
 * @param files - 文件列表
 * @returns 注册的工具名称列表
 */
async function autoDiscoverAndRegister(server: McpServer, files: GitHubFileContent[]): Promise<string[]> {
  const registeredTools: string[] = [];

  // 查找 markdown 文件作为潜在的提示词文件
  const markdownFiles = files.filter(f => f.name.endsWith('.md'));

  for (const file of markdownFiles) {
    const toolName = file.name.replace('.md', '').toLowerCase();

    server.registerTool(
      `auto-${toolName}`,
      {
        title: `Auto-discovered: ${toolName}`,
        description: `Auto-discovered tool from ${file.name}`,
        inputSchema: {
          input: z.string().describe('Input for the auto-discovered tool')
        }
      },
      async (args) => {
        const { input } = args as { input: string };
        return {
          content: [{
            type: 'text',
            text: `Executing auto-discovered tool ${toolName} with input: ${input}\n\nContent from ${file.name}:\n${file.content}`
          }]
        };
      }
    );

    registeredTools.push(`auto:${toolName}`);
  }

  return registeredTools;
}
