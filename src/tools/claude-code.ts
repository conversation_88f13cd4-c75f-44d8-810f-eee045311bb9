/**
 * Claude Code MCP Tools - Register individual sub-agents and commands as MCP tools
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import path from 'path';
import { parseGitHubRepo, validateTargetDir } from '../utils/validation.js';
import { cloneGitHubRepo } from '../utils/github.js';
import { ensureDir, getFilesInDir, fileExists, readFile } from '../utils/file.js';
import { processMarkdownFile } from '../utils/markdown.js';

import { GitHubFileContent, MarkdownFile } from '../types/github.js';

/**
 * Check and register tools from local .xclaude directory
 * @param server - MCP server instance
 * @param targetDir - Target directory path
 * @returns Whether local content was found and registered
 */
async function checkAndRegisterLocalTools(server: McpServer, targetDir: string): Promise<boolean> {
  try {
    const xclaudeExists = await fileExists(targetDir);
    if (!xclaudeExists) {
      return false;
    }

    let hasContent = false;

    // Check for agents directory
    const agentsDir = path.join(targetDir, 'agents');
    const agentsExists = await fileExists(agentsDir);

    if (agentsExists) {
      const agentFiles = await getFilesInDir(agentsDir);
      for (const filePath of agentFiles) {
        if (filePath.endsWith('.md')) {
          await registerAgentFromFile(server, filePath);
          hasContent = true;
        }
      }
    }

    // Check for commands directory
    const commandsDir = path.join(targetDir, 'commands');
    const commandsExists = await fileExists(commandsDir);

    if (commandsExists) {
      const commandFiles = await getFilesInDir(commandsDir);
      for (const filePath of commandFiles) {
        if (filePath.endsWith('.md')) {
          await registerCommandFromFile(server, filePath);
          hasContent = true;
        }
      }
    }

    return hasContent;
  } catch (error) {
    console.warn(`Failed to check local .xclaude directory: ${error}`);
    return false;
  }
}

/**
 * Download from GitHub and register tools
 * @param server - MCP server instance
 * @param githubRepo - GitHub repository
 * @param targetDir - Target directory
 */
async function downloadAndRegisterFromGitHub(server: McpServer, githubRepo: string, targetDir: string): Promise<void> {
  // Validate input parameters
  const repoInfo = parseGitHubRepo(githubRepo);
  const safeTargetDir = validateTargetDir(targetDir);

  // Download repository using git clone
  const files = await cloneGitHubRepo(repoInfo, safeTargetDir);

  if (files.length === 0) {
    throw new Error(`No markdown files found in repository ${repoInfo.owner}/${repoInfo.repo}`);
  }

  console.log(`Downloaded ${files.length} files from ${repoInfo.owner}/${repoInfo.repo}`);

  // Register tools from downloaded files
  await registerToolsFromFiles(server, files);
}

/**
 * Register tools from downloaded files
 * @param server - MCP server instance
 * @param files - Downloaded files
 */
async function registerToolsFromFiles(server: McpServer, files: GitHubFileContent[]): Promise<void> {
  const markdownFiles = files.filter(f => f.name.endsWith('.md'));

  for (const file of markdownFiles) {
    const processedFile = processMarkdownFile(file);

    // Determine if it's an agent or command based on path
    if (file.path.includes('agents/') || file.path.includes('agents\\')) {
      await registerAgentFromProcessedFile(server, processedFile);
    } else if (file.path.includes('commands/') || file.path.includes('commands\\')) {
      await registerCommandFromProcessedFile(server, processedFile);
    }
  }
}

/**
 * Initialize and register xclaude tools from local or remote sources
 * @param server - MCP server instance
 * @param githubRepo - Optional GitHub repository to download from if no local content
 */
export async function initializeClaudeCodeTools(server: McpServer, githubRepo?: string): Promise<void> {
  const targetDir = '.xclaude';

  try {
    // Check if local .xclaude directory exists and has content
    const hasLocalContent = await checkAndRegisterLocalTools(server, targetDir);

    if (hasLocalContent) {
      console.log('Registered tools from local .xclaude directory');
      return;
    }

    // If no local content and githubRepo is provided, download from GitHub
    if (githubRepo) {
      console.log(`No local .xclaude directory found. Downloading from ${githubRepo}...`);
      await downloadAndRegisterFromGitHub(server, githubRepo, targetDir);
      console.log('Successfully downloaded and registered tools from GitHub');
      return;
    }

    console.log('No local .xclaude directory found and no GitHub repository configured.');
  } catch (error) {
    console.error(`Failed to initialize xclaude tools: ${error}`);
  }
}

/**
 * Register agent from file path
 * @param server - MCP server instance
 * @param filePath - File path
 */
async function registerAgentFromFile(server: McpServer, filePath: string): Promise<void> {
  try {
    const content = await readFile(filePath);
    const processedFile = processMarkdownFile({
      name: path.basename(filePath),
      path: filePath,
      content
    });

    await registerAgentFromProcessedFile(server, processedFile);
  } catch (error) {
    console.warn(`Failed to register agent from ${filePath}: ${error}`);
  }
}

/**
 * Register command from file path
 * @param server - MCP server instance
 * @param filePath - File path
 */
async function registerCommandFromFile(server: McpServer, filePath: string): Promise<void> {
  try {
    const content = await readFile(filePath);
    const processedFile = processMarkdownFile({
      name: path.basename(filePath),
      path: filePath,
      content
    });

    await registerCommandFromProcessedFile(server, processedFile);
  } catch (error) {
    console.warn(`Failed to register command from ${filePath}: ${error}`);
  }
}

/**
 * Register agent from processed markdown file
 * @param server - MCP server instance
 * @param file - Processed markdown file
 */
async function registerAgentFromProcessedFile(server: McpServer, file: MarkdownFile): Promise<void> {
  const { frontmatter, markdownContent } = file;

  if (!frontmatter || !frontmatter.name || !frontmatter.description) {
    console.warn(`Agent file ${file.path} missing required frontmatter fields`);
    return;
  }

  const toolName = `${frontmatter.name}-agent`;

  server.registerTool(
    toolName,
    {
      title: `Agent: ${frontmatter.name}`,
      description: frontmatter.description,
      inputSchema: {
        task: z.string().describe('Task description for the agent')
      }
    },
    async (args) => {
      const { task } = args as { task: string };
      return {
        content: [{
          type: 'text',
          text: `Executing agent ${frontmatter.name} with task: ${task}\n\nSystem Prompt:\n${markdownContent}`
        }]
      };
    }
  );
}

/**
 * Register command from processed markdown file
 * @param server - MCP server instance
 * @param file - Processed markdown file
 */
async function registerCommandFromProcessedFile(server: McpServer, file: MarkdownFile): Promise<void> {
  const { frontmatter, markdownContent } = file;

  const name = file.name.replace('.md', '');
  const description = (frontmatter?.description) || markdownContent.split('\n')[0] || `Command: ${name}`;

  const toolName = `${name}-command`;

  server.registerTool(
    toolName,
    {
      title: `Command: ${name}`,
      description,
      inputSchema: {
        arguments: z.string().optional().describe('Arguments for the command (replaces $ARGUMENTS in content)')
      }
    },
    async (args) => {
      const { arguments: cmdArgs = '' } = args as { arguments?: string };

      // Replace $ARGUMENTS placeholder with actual arguments
      const processedContent = markdownContent.replace(/\$ARGUMENTS/g, cmdArgs);

      return {
        content: [{
          type: 'text',
          text: `Executing command ${name}${cmdArgs ? ` with arguments: ${cmdArgs}` : ''}\n\nContent:\n${processedContent}`
        }]
      };
    }
  );
}

/**
 * Register claude-code tools with optional GitHub repository configuration
 * @param server - MCP server instance
 * @param githubRepo - Optional GitHub repository to download from if no local content
 */
export async function registerClaudeCodeTool(server: McpServer, githubRepo?: string): Promise<void> {
  // 注册一个测试工具，确保 MCP 服务器有工具可用
  server.registerTool(
    'claude-code-status',
    {
      title: 'Claude Code Status',
      description: 'Get the status of claude-code tools',
      inputSchema: {}
    },
    async () => {
      return {
        content: [{
          type: 'text',
          text: `Claude Code MCP Tool Status:\n- GitHub Repository: ${githubRepo || 'Not configured'}\n- Local .xclaude directory: ${await fileExists('.xclaude') ? 'Found' : 'Not found'}`
        }]
      };
    }
  );

  // Auto-initialize from local directory or GitHub repository on startup
  await initializeClaudeCodeTools(server, githubRepo);
}
