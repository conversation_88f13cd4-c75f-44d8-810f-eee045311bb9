#!/usr/bin/env node

/**
 * Claude Code MCP 工具主入口文件
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { initializeClaudeCodeTools } from './tools/claude-code.js';

/**
 * 创建并启动 MCP 服务器
 */
function createServer(): McpServer {
  // 创建 MCP 服务器实例
  const server = new McpServer({
    name: 'claude-code-mcp',
    version: '1.0.0'
  });

  // Register claude-code tools with optional GitHub repository
  // You can configure a default GitHub repository here
  const defaultGitHubRepo = process.env.XCLAUDE_GITHUB_REPO; // e.g., "cexll/myclaude"
  initializeClaudeCodeTools(server, defaultGitHubRepo);

  return server;
}

/**
 * Start the MCP server
 */
export async function startServer(): Promise<void> {
  const server = createServer();// 创建 stdio 传输层
  const transport = new StdioServerTransport();

  // 启动服务器
  await server.connect(transport);

  console.log('Claude Code MCP server started successfully');
  console.log('Available tools:');
  console.log('- claude-code: Download and register claude-code sub-agents and slash-commands from GitHub repositories');
  // console.log("MCP Server running on stdio");
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 启动服务器
startServer().catch((error) => {
  console.error('Failed to start MCP server:', error);
  process.exit(1);
});
