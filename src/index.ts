#!/usr/bin/env node

/**
 * Claude Code MCP 工具主入口文件
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { registerClaudeCodeTool } from './tools/claude-code.js';

/**
 * 创建并启动 MCP 服务器
 */
async function main() {
  // 创建 MCP 服务器实例
  const server = new McpServer({
    name: 'claude-code-mcp',
    version: '1.0.0'
  });

  // 注册 claude-code 工具
  registerClaudeCodeTool(server);

  // 创建 stdio 传输层
  const transport = new StdioServerTransport();

  // 启动服务器
  await server.connect(transport);

  console.log('Claude Code MCP server started successfully');
  console.log('Available tools:');
  console.log('- claude-code: Download and register claude-code sub-agents and slash-commands from GitHub repositories');
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 启动服务器
main().catch((error) => {
  console.error('Failed to start MCP server:', error);
  process.exit(1);
});
